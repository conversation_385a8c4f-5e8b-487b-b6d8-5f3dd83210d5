import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Linking,
} from 'react-native';
import { checkAuthStatus, initiateAuth, getAuthUrl } from '../services/AuthService';

export default function AuthScreen({ navigation }) {
  const [loading, setLoading] = useState(false);
  const [authStatus, setAuthStatus] = useState(null);

  useEffect(() => {
    checkAuthentication();
  }, []);

  const checkAuthentication = async () => {
    setLoading(true);
    try {
      const status = await checkAuthStatus();
      setAuthStatus(status);
      
      if (status.isAuthenticated) {
        Alert.alert(
          'Already Authenticated',
          'You are already authenticated with Google Drive!',
          [
            {
              text: 'Go to Documents',
              onPress: () => navigation.navigate('Documents')
            }
          ]
        );
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAuthenticate = async () => {
    setLoading(true);
    try {
      // Try to initiate authentication
      const authData = await initiateAuth();
      
      if (authData.auth_url) {
        // If we get an auth URL, open it in browser
        Alert.alert(
          'Authentication Required',
          'You will be redirected to Google to authenticate. Please complete the authentication and return to the app.',
          [
            {
              text: 'Open Google Auth',
              onPress: () => Linking.openURL(authData.auth_url)
            },
            {
              text: 'Cancel',
              style: 'cancel'
            }
          ]
        );
      } else if (authData.success) {
        // Authentication successful
        Alert.alert(
          'Success',
          'Authentication successful!',
          [
            {
              text: 'Go to Documents',
              onPress: () => navigation.navigate('Documents')
            }
          ]
        );
      } else {
        Alert.alert('Error', 'Authentication failed. Please try again.');
      }
      
    } catch (error) {
      console.error('Authentication error:', error);
      Alert.alert(
        'Authentication Error',
        `Failed to authenticate: ${error.message}\n\nPlease check your Django backend is running and configured properly.`
      );
    } finally {
      setLoading(false);
    }
  };

  const handleGetAuthUrl = async () => {
    setLoading(true);
    try {
      const urlData = await getAuthUrl();
      
      if (urlData.auth_url) {
        Alert.alert(
          'Google Authentication',
          'Open this URL in your browser to authenticate with Google Drive.',
          [
            {
              text: 'Open URL',
              onPress: () => Linking.openURL(urlData.auth_url)
            },
            {
              text: 'Cancel',
              style: 'cancel'
            }
          ]
        );
      } else {
        Alert.alert('Error', 'Could not get authentication URL.');
      }
      
    } catch (error) {
      console.error('Error getting auth URL:', error);
      Alert.alert('Error', `Failed to get auth URL: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Google Drive Authentication</Text>
      
      <Text style={styles.description}>
        To access your Google Drive files, you need to authenticate with Google Drive first.
      </Text>

      {authStatus && (
        <View style={styles.statusContainer}>
          <Text style={styles.statusTitle}>Authentication Status:</Text>
          <Text style={[
            styles.statusText,
            { color: authStatus.isAuthenticated ? '#4CAF50' : '#F44336' }
          ]}>
            {authStatus.isAuthenticated ? '✅ Authenticated' : '❌ Not Authenticated'}
          </Text>
          {authStatus.error && (
            <Text style={styles.errorText}>{authStatus.error}</Text>
          )}
        </View>
      )}

      <View style={styles.buttonContainer}>
        <TouchableOpacity
          style={[styles.button, styles.primaryButton]}
          onPress={handleAuthenticate}
          disabled={loading}
        >
          {loading ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <Text style={styles.buttonText}>Authenticate with Google Drive</Text>
          )}
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={handleGetAuthUrl}
          disabled={loading}
        >
          <Text style={styles.secondaryButtonText}>Get Auth URL</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.secondaryButton]}
          onPress={checkAuthentication}
          disabled={loading}
        >
          <Text style={styles.secondaryButtonText}>Check Status</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.button, styles.tertiaryButton]}
          onPress={() => navigation.navigate('Documents')}
        >
          <Text style={styles.tertiaryButtonText}>Try Documents Anyway</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#333',
  },
  description: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
    lineHeight: 24,
  },
  statusContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    marginBottom: 30,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  statusTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#333',
  },
  statusText: {
    fontSize: 16,
    fontWeight: '500',
  },
  errorText: {
    fontSize: 14,
    color: '#F44336',
    marginTop: 5,
  },
  buttonContainer: {
    gap: 15,
  },
  button: {
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 50,
  },
  primaryButton: {
    backgroundColor: '#4285F4',
  },
  secondaryButton: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#4285F4',
  },
  tertiaryButton: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: '#ccc',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
  },
  secondaryButtonText: {
    color: '#4285F4',
    fontSize: 16,
    fontWeight: '500',
  },
  tertiaryButtonText: {
    color: '#666',
    fontSize: 16,
    fontWeight: '500',
  },
});
