{"version": 3, "names": ["getHeaderTitle", "Header", "SafeAreaProviderCompat", "Screen", "StackActions", "useLocale", "React", "Animated", "Platform", "StyleSheet", "SafeAreaInsetsContext", "FadeTransition", "ShiftTransition", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "useAnimatedHashMap", "BottomTabBar", "getTabBarHeight", "MaybeScreen", "MaybeScreenContainer", "jsx", "_jsx", "jsxs", "_jsxs", "EPSILON", "STATE_INACTIVE", "STATE_TRANSITIONING_OR_BELOW_TOP", "STATE_ON_TOP", "NAMED_TRANSITIONS_PRESETS", "fade", "shift", "none", "sceneStyleInterpolator", "undefined", "transitionSpec", "animation", "config", "duration", "useNativeDriver", "OS", "hasAnimation", "options", "Boolean", "renderTabBarDefault", "props", "BottomTabView", "tabBar", "state", "navigation", "descriptors", "safeAreaInsets", "detachInactiveScreens", "focusedRouteKey", "routes", "index", "key", "direction", "loaded", "setLoaded", "useState", "includes", "previousRouteKeyRef", "useRef", "tabAnims", "useEffect", "previousRouteKey", "current", "popToTopAction", "popToTopOnBlur", "prevRoute", "find", "route", "type", "popToTop", "target", "animateToIndex", "emit", "parallel", "map", "spec", "toValue", "filter", "start", "finished", "dispatch", "dimensions", "initialMetrics", "frame", "tabBarHeight", "setTabBarHeight", "insets", "style", "tabBarStyle", "renderTabBar", "Consumer", "children", "top", "right", "bottom", "left", "hasTwoStates", "some", "tabBarPosition", "flexDirection", "Provider", "value", "enabled", "styles", "screens", "descriptor", "lazy", "isFocused", "isPreloaded", "preloadedRouteKeys", "freezeOnBlur", "header", "layout", "title", "name", "headerShown", "headerStatusBarHeight", "headerTransparent", "sceneStyle", "customSceneStyle", "progress", "animationEnabled", "activityState", "interpolate", "inputRange", "outputRange", "extrapolate", "absoluteFill", "zIndex", "active", "shouldFreeze", "focused", "render", "create", "flex", "overflow"], "sourceRoot": "../../../src", "sources": ["views/BottomTabView.tsx"], "mappings": ";;AAAA,SACEA,cAAc,EACdC,MAAM,EACNC,sBAAsB,EACtBC,MAAM,QACD,4BAA4B;AACnC,SAGEC,YAAY,EAEZC,SAAS,QACJ,0BAA0B;AACjC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAC7D,SAASC,qBAAqB,QAAQ,gCAAgC;AAEtE,SACEC,cAAc,EACdC,eAAe,QACV,2CAAwC;AAU/C,SAASC,iCAAiC,QAAQ,+CAA4C;AAC9F,SAASC,yBAAyB,QAAQ,uCAAoC;AAC9E,SAASC,kBAAkB,QAAQ,gCAA6B;AAChE,SAASC,YAAY,EAAEC,eAAe,QAAQ,mBAAgB;AAC9D,SAASC,WAAW,EAAEC,oBAAoB,QAAQ,qBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAQrE,MAAMC,OAAO,GAAG,IAAI;AACpB,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,gCAAgC,GAAG,CAAC;AAC1C,MAAMC,YAAY,GAAG,CAAC;AAEtB,MAAMC,yBAAyB,GAAG;EAChCC,IAAI,EAAElB,cAAc;EACpBmB,KAAK,EAAElB,eAAe;EACtBmB,IAAI,EAAE;IACJC,sBAAsB,EAAEC,SAAS;IACjCC,cAAc,EAAE;MACdC,SAAS,EAAE,QAAQ;MACnBC,MAAM,EAAE;QAAEC,QAAQ,EAAE;MAAE;IACxB;EACF;AACF,CAAU;AAEV,MAAMC,eAAe,GAAG9B,QAAQ,CAAC+B,EAAE,KAAK,KAAK;AAE7C,MAAMC,YAAY,GAAIC,OAAmC,IAAK;EAC5D,MAAM;IAAEN,SAAS;IAAED;EAAe,CAAC,GAAGO,OAAO;EAE7C,IAAIN,SAAS,EAAE;IACb,OAAOA,SAAS,KAAK,MAAM;EAC7B;EAEA,OAAOO,OAAO,CAACR,cAAc,CAAC;AAChC,CAAC;AAED,MAAMS,mBAAmB,GAAIC,KAAwB,iBACnDvB,IAAA,CAACL,YAAY;EAAA,GAAK4B;AAAK,CAAG,CAC3B;AAED,OAAO,SAASC,aAAaA,CAACD,KAAY,EAAE;EAC1C,MAAM;IACJE,MAAM,GAAGH,mBAAmB;IAC5BI,KAAK;IACLC,UAAU;IACVC,WAAW;IACXC,cAAc;IACdC,qBAAqB,GAAG3C,QAAQ,CAAC+B,EAAE,KAAK,KAAK,IAC3C/B,QAAQ,CAAC+B,EAAE,KAAK,SAAS,IACzB/B,QAAQ,CAAC+B,EAAE,KAAK;EACpB,CAAC,GAAGK,KAAK;EAET,MAAMQ,eAAe,GAAGL,KAAK,CAACM,MAAM,CAACN,KAAK,CAACO,KAAK,CAAC,CAACC,GAAG;EAErD,MAAM;IAAEC;EAAU,CAAC,GAAGnD,SAAS,CAAC,CAAC;;EAEjC;AACF;AACA;EACE,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGpD,KAAK,CAACqD,QAAQ,CAAC,CAACP,eAAe,CAAC,CAAC;EAE7D,IAAI,CAACK,MAAM,CAACG,QAAQ,CAACR,eAAe,CAAC,EAAE;IACrC;IACAM,SAAS,CAAC,CAAC,GAAGD,MAAM,EAAEL,eAAe,CAAC,CAAC;EACzC;EAEA,MAAMS,mBAAmB,GAAGvD,KAAK,CAACwD,MAAM,CAACV,eAAe,CAAC;EACzD,MAAMW,QAAQ,GAAGhD,kBAAkB,CAACgC,KAAK,CAAC;EAE1CzC,KAAK,CAAC0D,SAAS,CAAC,MAAM;IACpB,MAAMC,gBAAgB,GAAGJ,mBAAmB,CAACK,OAAO;IAEpD,IAAIC,cAA4C;IAEhD,IACEF,gBAAgB,KAAKb,eAAe,IACpCH,WAAW,CAACgB,gBAAgB,CAAC,EAAExB,OAAO,CAAC2B,cAAc,EACrD;MACA,MAAMC,SAAS,GAAGtB,KAAK,CAACM,MAAM,CAACiB,IAAI,CAChCC,KAAK,IAAKA,KAAK,CAAChB,GAAG,KAAKU,gBAC3B,CAAC;MAED,IAAII,SAAS,EAAEtB,KAAK,EAAEyB,IAAI,KAAK,OAAO,IAAIH,SAAS,CAACtB,KAAK,CAACQ,GAAG,EAAE;QAC7DY,cAAc,GAAG;UACf,GAAG/D,YAAY,CAACqE,QAAQ,CAAC,CAAC;UAC1BC,MAAM,EAAEL,SAAS,CAACtB,KAAK,CAACQ;QAC1B,CAAC;MACH;IACF;IAEA,MAAMoB,cAAc,GAAGA,CAAA,KAAM;MAC3B,IAAIV,gBAAgB,KAAKb,eAAe,EAAE;QACxCJ,UAAU,CAAC4B,IAAI,CAAC;UACdJ,IAAI,EAAE,iBAAiB;UACvBE,MAAM,EAAEtB;QACV,CAAC,CAAC;MACJ;MAEA7C,QAAQ,CAACsE,QAAQ,CACf9B,KAAK,CAACM,MAAM,CACTyB,GAAG,CAAC,CAACP,KAAK,EAAEjB,KAAK,KAAK;QACrB,MAAM;UAAEb;QAAQ,CAAC,GAAGQ,WAAW,CAACsB,KAAK,CAAChB,GAAG,CAAC;QAC1C,MAAM;UACJpB,SAAS,GAAG,MAAM;UAClBD,cAAc,GAAGN,yBAAyB,CAACO,SAAS,CAAC,CAClDD;QACL,CAAC,GAAGO,OAAO;QAEX,IAAIsC,IAAI,GAAG7C,cAAc;QAEzB,IACEqC,KAAK,CAAChB,GAAG,KAAKU,gBAAgB,IAC9BM,KAAK,CAAChB,GAAG,KAAKH,eAAe,EAC7B;UACA;UACA;UACA2B,IAAI,GAAGnD,yBAAyB,CAACG,IAAI,CAACG,cAAc;QACtD;QAEA6C,IAAI,GAAGA,IAAI,IAAInD,yBAAyB,CAACG,IAAI,CAACG,cAAc;QAE5D,MAAM8C,OAAO,GACX1B,KAAK,KAAKP,KAAK,CAACO,KAAK,GAAG,CAAC,GAAGA,KAAK,IAAIP,KAAK,CAACO,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3D,OAAO/C,QAAQ,CAACwE,IAAI,CAAC5C,SAAS,CAAC,CAAC4B,QAAQ,CAACQ,KAAK,CAAChB,GAAG,CAAC,EAAE;UACnD,GAAGwB,IAAI,CAAC3C,MAAM;UACd4C,OAAO;UACP1C;QACF,CAAC,CAAC;MACJ,CAAC,CAAC,CACD2C,MAAM,CAACvC,OAAO,CACnB,CAAC,CAACwC,KAAK,CAAC,CAAC;QAAEC;MAAS,CAAC,KAAK;QACxB,IAAIA,QAAQ,IAAIhB,cAAc,EAAE;UAC9BnB,UAAU,CAACoC,QAAQ,CAACjB,cAAc,CAAC;QACrC;QAEA,IAAIF,gBAAgB,KAAKb,eAAe,EAAE;UACxCJ,UAAU,CAAC4B,IAAI,CAAC;YACdJ,IAAI,EAAE,eAAe;YACrBE,MAAM,EAAEtB;UACV,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ,CAAC;IAEDuB,cAAc,CAAC,CAAC;IAEhBd,mBAAmB,CAACK,OAAO,GAAGd,eAAe;EAC/C,CAAC,EAAE,CACDH,WAAW,EACXG,eAAe,EACfJ,UAAU,EACVD,KAAK,CAACO,KAAK,EACXP,KAAK,CAACM,MAAM,EACZU,QAAQ,CACT,CAAC;EAEF,MAAMsB,UAAU,GAAGnF,sBAAsB,CAACoF,cAAc,CAACC,KAAK;EAC9D,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGnF,KAAK,CAACqD,QAAQ,CAAC,MACrD1C,eAAe,CAAC;IACd8B,KAAK;IACLE,WAAW;IACXoC,UAAU;IACVK,MAAM,EAAE;MACN,GAAGxF,sBAAsB,CAACoF,cAAc,CAACI,MAAM;MAC/C,GAAG9C,KAAK,CAACM;IACX,CAAC;IACDyC,KAAK,EAAE1C,WAAW,CAACF,KAAK,CAACM,MAAM,CAACN,KAAK,CAACO,KAAK,CAAC,CAACC,GAAG,CAAC,CAACd,OAAO,CAACmD;EAC5D,CAAC,CACH,CAAC;EAED,MAAMC,YAAY,GAAGA,CAAA,KAAM;IACzB,oBACExE,IAAA,CAACX,qBAAqB,CAACoF,QAAQ;MAAAC,QAAA,EAC3BL,MAAM,IACN5C,MAAM,CAAC;QACLC,KAAK,EAAEA,KAAK;QACZE,WAAW,EAAEA,WAAW;QACxBD,UAAU,EAAEA,UAAU;QACtB0C,MAAM,EAAE;UACNM,GAAG,EAAE9C,cAAc,EAAE8C,GAAG,IAAIN,MAAM,EAAEM,GAAG,IAAI,CAAC;UAC5CC,KAAK,EAAE/C,cAAc,EAAE+C,KAAK,IAAIP,MAAM,EAAEO,KAAK,IAAI,CAAC;UAClDC,MAAM,EAAEhD,cAAc,EAAEgD,MAAM,IAAIR,MAAM,EAAEQ,MAAM,IAAI,CAAC;UACrDC,IAAI,EAAEjD,cAAc,EAAEiD,IAAI,IAAIT,MAAM,EAAES,IAAI,IAAI;QAChD;MACF,CAAC;IAAC,CAE0B,CAAC;EAErC,CAAC;EAED,MAAM;IAAE9C;EAAO,CAAC,GAAGN,KAAK;;EAExB;EACA,MAAMqD,YAAY,GAAG,CAAC/C,MAAM,CAACgD,IAAI,CAAE9B,KAAK,IACtC/B,YAAY,CAACS,WAAW,CAACsB,KAAK,CAAChB,GAAG,CAAC,CAACd,OAAO,CAC7C,CAAC;EAED,MAAM;IAAE6D,cAAc,GAAG;EAAS,CAAC,GAAGrD,WAAW,CAACG,eAAe,CAAC,CAACX,OAAO;EAE1E,oBACElB,KAAA,CAACrB,sBAAsB;IACrByF,KAAK,EAAE;MACLY,aAAa,EACXD,cAAc,KAAK,MAAM,IAAIA,cAAc,KAAK,OAAO,GAClDA,cAAc,KAAK,MAAM,IAAI9C,SAAS,KAAK,KAAK,IAChD8C,cAAc,KAAK,OAAO,IAAI9C,SAAS,KAAK,KAAM,GACjD,aAAa,GACb,KAAK,GACP;IACR,CAAE;IAAAuC,QAAA,GAEDO,cAAc,KAAK,KAAK,gBACvBjF,IAAA,CAACR,iCAAiC,CAAC2F,QAAQ;MAACC,KAAK,EAAEhB,eAAgB;MAAAM,QAAA,EAChEF,YAAY,CAAC;IAAC,CAC2B,CAAC,GAC3C,IAAI,eACRxE,IAAA,CAACF,oBAAoB;MACnBuF,OAAO,EAAEvD,qBAAsB;MAC/BiD,YAAY,EAAEA,YAAa;MAC3BT,KAAK,EAAEgB,MAAM,CAACC,OAAQ;MAAAb,QAAA,EAErB1C,MAAM,CAACyB,GAAG,CAAC,CAACP,KAAK,EAAEjB,KAAK,KAAK;QAC5B,MAAMuD,UAAU,GAAG5D,WAAW,CAACsB,KAAK,CAAChB,GAAG,CAAC;QACzC,MAAM;UACJuD,IAAI,GAAG,IAAI;UACX3E,SAAS,GAAG,MAAM;UAClBH,sBAAsB,GAAGJ,yBAAyB,CAACO,SAAS,CAAC,CAC1DH;QACL,CAAC,GAAG6E,UAAU,CAACpE,OAAO;QACtB,MAAMsE,SAAS,GAAGhE,KAAK,CAACO,KAAK,KAAKA,KAAK;QACvC,MAAM0D,WAAW,GAAGjE,KAAK,CAACkE,kBAAkB,CAACrD,QAAQ,CAACW,KAAK,CAAChB,GAAG,CAAC;QAEhE,IACEuD,IAAI,IACJ,CAACrD,MAAM,CAACG,QAAQ,CAACW,KAAK,CAAChB,GAAG,CAAC,IAC3B,CAACwD,SAAS,IACV,CAACC,WAAW,EACZ;UACA;UACA,OAAO,IAAI;QACb;QAEA,MAAM;UACJE,YAAY;UACZC,MAAM,GAAGA,CAAC;YAAEC,MAAM;YAAE3E;UAA8B,CAAC,kBACjDpB,IAAA,CAACpB,MAAM;YAAA,GACDwC,OAAO;YACX2E,MAAM,EAAEA,MAAO;YACfC,KAAK,EAAErH,cAAc,CAACyC,OAAO,EAAE8B,KAAK,CAAC+C,IAAI;UAAE,CAC5C,CACF;UACDC,WAAW;UACXC,qBAAqB;UACrBC,iBAAiB;UACjBC,UAAU,EAAEC;QACd,CAAC,GAAGd,UAAU,CAACpE,OAAO;QAEtB,MAAM;UAAEiF;QAAW,CAAC,GAClB1F,sBAAsB,GAAG;UACvBkC,OAAO,EAAE;YACP0D,QAAQ,EAAE7D,QAAQ,CAACQ,KAAK,CAAChB,GAAG;UAC9B;QACF,CAAC,CAAC,IAAI,CAAC,CAAC;QAEV,MAAMsE,gBAAgB,GAAGrF,YAAY,CAACqE,UAAU,CAACpE,OAAO,CAAC;QACzD,MAAMqF,aAAa,GAAGf,SAAS,GAC3BpF,YAAY,CAAC;QAAA,EACbkG,gBAAgB,CAAC;QAAA,EACf9D,QAAQ,CAACQ,KAAK,CAAChB,GAAG,CAAC,CAACwE,WAAW,CAAC;UAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,GAAGxG,OAAO,EAAE,CAAC,CAAC;UAC/ByG,WAAW,EAAE,CACXvG,gCAAgC;UAAE;UAClCA,gCAAgC,EAChCD,cAAc,CAAE;UAAA,CACjB;UACDyG,WAAW,EAAE;QACf,CAAC,CAAC,GACFzG,cAAc;QAEpB,oBACEJ,IAAA,CAACH,WAAW;UAEVyE,KAAK,EAAE,CAAClF,UAAU,CAAC0H,YAAY,EAAE;YAAEC,MAAM,EAAErB,SAAS,GAAG,CAAC,GAAG,CAAC;UAAE,CAAC,CAAE;UACjEsB,MAAM,EAAEP,aAAc;UACtBpB,OAAO,EAAEvD,qBAAsB;UAC/B+D,YAAY,EAAEA,YAAa;UAC3BoB,YAAY,EAAER,aAAa,KAAKrG,cAAc,IAAI,CAACuF,WAAY;UAAAjB,QAAA,eAE/D1E,IAAA,CAACP,yBAAyB,CAAC0F,QAAQ;YACjCC,KAAK,EAAEH,cAAc,KAAK,QAAQ,GAAGd,YAAY,GAAG,CAAE;YAAAO,QAAA,eAEtD1E,IAAA,CAAClB,MAAM;cACLoI,OAAO,EAAExB,SAAU;cACnBxC,KAAK,EAAEsC,UAAU,CAACtC,KAAM;cACxBvB,UAAU,EAAE6D,UAAU,CAAC7D,UAAW;cAClCuE,WAAW,EAAEA,WAAY;cACzBC,qBAAqB,EAAEA,qBAAsB;cAC7CC,iBAAiB,EAAEA,iBAAkB;cACrCN,MAAM,EAAEA,MAAM,CAAC;gBACbC,MAAM,EAAE/B,UAAU;gBAClBd,KAAK,EAAEsC,UAAU,CAACtC,KAAK;gBACvBvB,UAAU,EACR6D,UAAU,CAAC7D,UAAoD;gBACjEP,OAAO,EAAEoE,UAAU,CAACpE;cACtB,CAAC,CAAE;cACHkD,KAAK,EAAE,CAACgC,gBAAgB,EAAEE,gBAAgB,IAAIH,UAAU,CAAE;cAAA3B,QAAA,EAEzDc,UAAU,CAAC2B,MAAM,CAAC;YAAC,CACd;UAAC,CACyB;QAAC,GA5BhCjE,KAAK,CAAChB,GA6BA,CAAC;MAElB,CAAC;IAAC,CACkB,CAAC,EACtB+C,cAAc,KAAK,KAAK,gBACvBjF,IAAA,CAACR,iCAAiC,CAAC2F,QAAQ;MAACC,KAAK,EAAEhB,eAAgB;MAAAM,QAAA,EAChEF,YAAY,CAAC;IAAC,CAC2B,CAAC,GAC3C,IAAI;EAAA,CACc,CAAC;AAE7B;AAEA,MAAMc,MAAM,GAAGlG,UAAU,CAACgI,MAAM,CAAC;EAC/B7B,OAAO,EAAE;IACP8B,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}