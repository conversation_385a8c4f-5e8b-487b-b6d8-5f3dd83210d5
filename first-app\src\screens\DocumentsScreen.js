import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, Linking, ActivityIndicator, Alert, TouchableOpacity, SafeAreaView } from 'react-native';
import { fetchDriveFiles } from '../services/DocumentsService';

export default function DocumentsScreen({ navigation }) {
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadFiles = async () => {
    setLoading(true);
    try {
      const data = await fetchDriveFiles();
      setFiles(Array.isArray(data.files) ? data.files : []);
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFiles();
  }, []);

  const renderItem = ({ item }) => (
    <View style={styles.itemContainer}>
      <View style={styles.iconCircle}>
        <Text style={styles.fileIcon}>📄</Text>
      </View>
      <View style={{ flex: 1 }}>
        <Text style={styles.fileName}>{item.file_name}</Text>
        <Text style={styles.folderName}>📁 {item.folder_name}</Text>
        <TouchableOpacity onPress={() => Linking.openURL(item.file_link)}>
          <Text style={styles.link}>🔗 Open File</Text>
        </TouchableOpacity>
        <Text style={styles.uploadAt}>Uploaded: {new Date(item.upload_at).toLocaleString()}</Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#4285F4" />
      </View>
    );
  }

  return (
    <SafeAreaView style={styles.safeArea}>
      <View style={styles.headerAlt}>
  <View style={styles.headerIconCircleAlt}>
    <Text style={styles.headerIconAlt}>📁</Text>
  </View>
  <Text style={styles.headerTitleAlt}>My Documents</Text>
</View>
      <View style={styles.container}>
        <FlatList
          data={files}
          keyExtractor={(item) => item.file_id}
          renderItem={renderItem}
          ListEmptyComponent={<Text style={styles.emptyText}>No documents found.</Text>}
          contentContainerStyle={files.length === 0 && { flex: 1, justifyContent: 'center' }}
        />
        
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: { flex: 1, backgroundColor: '#f4f6fb' },
  header: {
  flexDirection: 'row',
  alignItems: 'center',
  justifyContent: 'center',
  paddingTop: 28,
  paddingBottom: 20,
  backgroundColor: '#4285F4',
  borderBottomLeftRadius: 18,
  borderBottomRightRadius: 18,
  elevation: 4,
  shadowColor: '#4285F4',
  shadowOpacity: 0.10,
  shadowRadius: 8,
  shadowOffset: { width: 0, height: 2 },
},
headerAlt: {
  flexDirection: 'row',
  alignItems: 'center',
  backgroundColor: '#fff',
  paddingTop: 28,
  paddingBottom: 18,
  paddingHorizontal: 18,
  borderBottomWidth: 1,
  borderBottomColor: '#e3eafc',
  elevation: 2,
  shadowColor: '#4285F4',
  shadowOpacity: 0.06,
  shadowRadius: 6,
  shadowOffset: { width: 0, height: 2 },
},
headerIconCircleAlt: {
  width: 44,
  height: 44,
  borderRadius: 22,
  backgroundColor: '#e3eafc',
  justifyContent: 'center',
  alignItems: 'center',
  marginRight: 14,
},
headerIconAlt: {
  fontSize: 24,
  color: '#4285F4',
},
headerTitleAlt: {
  color: '#222',
  fontSize: 24,
  fontWeight: 'bold',
  letterSpacing: 0.5,
},
  container: { flex: 1, padding: 18 },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f4f6fb',
  },
  itemContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderRadius: 18,
    padding: 18,
    marginBottom: 18,
    elevation: 1,
    shadowColor: '#4285F4',
    shadowOpacity: 0.06,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e3eafc',
  },
  iconCircle: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: '#e3eafc',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 18,
    borderWidth: 1,
    borderColor: '#c6dafc',
  },
  fileIcon: {
    fontSize: 28,
    color: '#4285F4',
  },
  fileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#222',
    marginBottom: 2,
  },
  folderName: {
    fontSize: 15,
    color: '#5f6368',
    marginBottom: 8,
  },
  link: {
    color: '#4285F4',
    fontWeight: '600',
    textDecorationLine: 'underline',
    fontSize: 16,
    marginBottom: 6,
  },
  uploadAt: {
    color: '#888',
    fontSize: 13,
  },
  emptyText: {
    textAlign: 'center',
    color: '#bbb',
    fontSize: 17,
    marginTop: 40,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 18,
  },
  button: {
    flex: 1,
    backgroundColor: '#4285F4',
    paddingVertical: 15,
    marginHorizontal: 10,
    borderRadius: 30,
    alignItems: 'center',
    elevation: 2,
    shadowColor: '#4285F4',
    shadowOpacity: 0.10,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  backButton: {
    backgroundColor: '#fff',
    borderWidth: 1.5,
    borderColor: '#4285F4',
  },
  buttonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
    letterSpacing: 0.5,
  },
  backButtonText: {
    color: '#4285F4',
  },
});