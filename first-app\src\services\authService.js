/**
 * Authentication Service for Django Backend
 * Uses /auth/ endpoint for authentication
 */

const API_CONFIG = {
  BASE_URL: 'https://hedgehog-enjoyed-evidently.ngrok-free.app',
  ENDPOINTS: {
    AUTH: '/auth/',
  },
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'ngrok-skip-browser-warning': 'true',
    'X-Requested-With': 'XMLHttpRequest',
  }
};

// Helper function to make API requests
async function makeApiRequest(endpoint, options = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;

  const defaultOptions = {
    method: 'GET',
    headers: {
      ...API_CONFIG.HEADERS,
      ...options.headers,
    },
  };

  const requestOptions = { ...defaultOptions, ...options };

  console.log(`Making request to: ${url}`);
  console.log('Request options:', requestOptions);

  try {
    const response = await fetch(url, requestOptions);

    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, response.headers);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    // Check if response is JSON
    const contentType = response.headers.get('content-type');
    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('Response data:', data);
      return data;
    } else {
      // If not JSON, get text to see what was returned
      const text = await response.text();
      console.log('Response text:', text.substring(0, 200) + '...');

      // Check if it's an HTML login page
      if (text.includes('<html') || text.includes('login') || text.includes('authenticate')) {
        throw new Error('Authentication required: Server returned login page. Please authenticate your backend with Google Drive.');
      }

      throw new Error('Server returned non-JSON response');
    }

  } catch (error) {
    console.error('API request failed:', error.message);
    throw error;
  }
}

// Check authentication status
export async function checkAuthStatus() {
  try {
    const data = await makeApiRequest(API_CONFIG.ENDPOINTS.AUTH);
    return {
      isAuthenticated: true,
      data: data
    };
  } catch (error) {
    console.error('Auth check failed:', error.message);
    return {
      isAuthenticated: false,
      error: error.message
    };
  }
}

// Initiate authentication with Google Drive
export async function initiateAuth() {
  try {
    const data = await makeApiRequest(API_CONFIG.ENDPOINTS.AUTH, {
      method: 'POST',
    });
    return data;
  } catch (error) {
    console.error('Auth initiation failed:', error.message);
    throw error;
  }
}

// Get authentication URL for Google Drive
export async function getAuthUrl() {
  try {
    const data = await makeApiRequest(`${API_CONFIG.ENDPOINTS.AUTH}url/`);
    return data;
  } catch (error) {
    console.error('Failed to get auth URL:', error.message);
    throw error;
  }
}

// Complete authentication with authorization code
export async function completeAuth(authCode) {
  try {
    const data = await makeApiRequest(`${API_CONFIG.ENDPOINTS.AUTH}callback/`, {
      method: 'POST',
      body: JSON.stringify({
        code: authCode
      })
    });
    return data;
  } catch (error) {
    console.error('Auth completion failed:', error.message);
    throw error;
  }
}