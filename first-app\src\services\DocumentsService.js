/**
 * CONFIGURATION FOR YOUR DJANGO BACKEND
 *
 * Based on your Django URLs, you have these endpoint options:
 *
 * 1. /drive/api/documents/ - Function-based API view
 * 2. /drive/api/documents-class/ - Class-based API view
 * 3. /drive/list/ - List drive files
 * 4. /drive/documents/ - Template view (returns HTML, not for API)
 *
 * Choose the one that returns JSON data for your mobile app.
 */

const API_CONFIG = {
  // Update this URL to your current ngrok or server URL
  BASE_URL: 'https://hedgehog-enjoyed-evidently.ngrok-free.app',
  ENDPOINTS: {
    // Try the list endpoint first (might not require authentication)
    DRIVE_FILES: '/drive/list/',

    // Alternative endpoints you can try if the above doesn't work:
    // DRIVE_FILES: '/drive/api/documents/',
    // DRIVE_FILES: '/drive/api/documents-class/',
  },
  // Add authentication headers if your backend requires them
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add ngrok bypass header to avoid browser warning page
    'ngrok-skip-browser-warning': 'true',
    // Disable CSRF for API requests (common for mobile apps)
    'X-Requested-With': 'XMLHttpRequest',
    // Add authorization header if needed:
    // 'Authorization': 'Bearer YOUR_TOKEN_HERE',
  }
};

// Helper function to make API requests
async function makeApiRequest(endpoint, options = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;

  const defaultOptions = {
    method: 'GET',
    headers: {
      ...API_CONFIG.HEADERS,
      ...options.headers,
    },
    ...options,
  };

  console.log(`Making API request to: ${url}`);

  try {
    const response = await fetch(url, defaultOptions);

    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));

    const contentType = response.headers.get('content-type') || '';

    // Check if response is HTML (authentication redirect)
    if (contentType.includes('text/html')) {
      const htmlText = await response.text();
      if (htmlText.includes('Google Drive Authentication') || htmlText.includes('<title>')) {
        throw new Error('Authentication required: Server returned login page. Please authenticate your backend with Google Drive.');
      }
      throw new Error(`Server returned HTML instead of JSON. This usually means authentication is required.`);
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error ${response.status}:`, errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (contentType.includes('application/json')) {
      const data = await response.json();
      console.log('API Response data:', data);
      return data;
    } else {
      const text = await response.text();
      console.warn('Non-JSON response received:', text.slice(0, 100));
      throw new Error(`Server returned unexpected content type: ${contentType}`);
    }
  } catch (error) {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error(`Network error: Unable to connect to server at ${url}. Please check if the server is running.`);
    }
    throw error;
  }
}

// Main function to fetch drive files
export async function fetchDriveFiles() {
  try {
    const data = await makeApiRequest(API_CONFIG.ENDPOINTS.DRIVE_FILES);

    // Validate the response structure
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format: Expected an object');
    }

    // Handle different possible response formats from your Django backend
    let files = [];

    if (Array.isArray(data)) {
      // If the response is directly an array of files
      files = data;
    } else if (data.files && Array.isArray(data.files)) {
      // If the response has a 'files' property
      files = data.files;
    } else if (data.data && Array.isArray(data.data)) {
      // If the response has a 'data' property
      files = data.data;
    } else if (data.results && Array.isArray(data.results)) {
      // If the response has a 'results' property (Django REST framework)
      files = data.results;
    } else if (data.documents && Array.isArray(data.documents)) {
      // If the response has a 'documents' property
      files = data.documents;
    } else {
      console.warn('Unexpected response format:', data);
      throw new Error('Response does not contain a recognizable files array');
    }

    console.log(`Successfully fetched ${files.length} files`);
    return { files };

  } catch (error) {
    console.error('Error fetching drive files:', error.message);

    // Re-throw the error instead of returning mock data
    throw error;
  }
}

// Additional API functions based on your Django backend

// Get file content
export async function getFileContent(fileId) {
  try {
    const data = await makeApiRequest(`/drive/content/${fileId}/`);
    return data;
  } catch (error) {
    console.error('Error fetching file content:', error.message);
    throw error;
  }
}

// Download file
export async function downloadFile(fileId) {
  try {
    const data = await makeApiRequest(`/drive/download/${fileId}/`);
    return data;
  } catch (error) {
    console.error('Error downloading file:', error.message);
    throw error;
  }
}

// Delete file
export async function deleteFile(fileId) {
  try {
    const data = await makeApiRequest(`/drive/delete/${fileId}/`, {
      method: 'DELETE',
    });
    return data;
  } catch (error) {
    console.error('Error deleting file:', error.message);
    throw error;
  }
}

// Upload file
export async function uploadFile(formData) {
  try {
    const data = await makeApiRequest('/drive/upload/', {
      method: 'POST',
      body: formData,
      headers: {
        // Don't set Content-Type for FormData, let the browser set it
        'Accept': 'application/json',
        'ngrok-skip-browser-warning': 'true',
      }
    });
    return data;
  } catch (error) {
    console.error('Error uploading file:', error.message);
    throw error;
  }
}

// Create folder
export async function createFolder(folderName, parentId = null) {
  try {
    const data = await makeApiRequest('/drive/create-folder/', {
      method: 'POST',
      body: JSON.stringify({
        name: folderName,
        parent_id: parentId
      })
    });
    return data;
  } catch (error) {
    console.error('Error creating folder:', error.message);
    throw error;
  }
}