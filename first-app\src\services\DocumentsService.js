// Configuration - Update these values for your backend
const API_CONFIG = {
  // Update this URL to your current ngrok or server URL
  BASE_URL: 'https://hedgehog-enjoyed-evidently.ngrok-free.app',
  ENDPOINTS: {
    DRIVE_FILES: '/drive/list/databaseMobile/',
  },
  // Add authentication headers if your backend requires them
  HEADERS: {
    'Content-Type': 'application/json',
    // Add authorization header if needed:
    // 'Authorization': 'Bearer YOUR_TOKEN_HERE',
    // Add ngrok bypass header if needed:
    // 'ngrok-skip-browser-warning': 'true',
  }
};

// Helper function to make API requests
async function makeApiRequest(endpoint, options = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;

  const defaultOptions = {
    method: 'GET',
    headers: {
      ...API_CONFIG.HEADERS,
      ...options.headers,
    },
    ...options,
  };

  console.log(`Making API request to: ${url}`);

  try {
    const response = await fetch(url, defaultOptions);

    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error ${response.status}:`, errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}\nResponse: ${errorText.slice(0, 200)}`);
    }

    const contentType = response.headers.get('content-type');

    if (contentType && contentType.includes('application/json')) {
      const data = await response.json();
      console.log('API Response data:', data);
      return data;
    } else {
      const text = await response.text();
      console.warn('Non-JSON response received:', text.slice(0, 100));
      throw new Error(`Server returned non-JSON response: ${contentType}\nContent: ${text.slice(0, 100)}`);
    }
  } catch (error) {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error(`Network error: Unable to connect to server at ${url}. Please check if the server is running.`);
    }
    throw error;
  }
}

export async function fetchDriveFiles() {
  try {
    const data = await makeApiRequest(API_CONFIG.ENDPOINTS.DRIVE_FILES);

    // Validate the response structure
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid response format: Expected an object');
    }

    // Handle different possible response formats from your backend
    let files = [];

    if (Array.isArray(data)) {
      // If the response is directly an array of files
      files = data;
    } else if (data.files && Array.isArray(data.files)) {
      // If the response has a 'files' property
      files = data.files;
    } else if (data.data && Array.isArray(data.data)) {
      // If the response has a 'data' property
      files = data.data;
    } else if (data.results && Array.isArray(data.results)) {
      // If the response has a 'results' property
      files = data.results;
    } else {
      console.warn('Unexpected response format:', data);
      throw new Error('Response does not contain a recognizable files array');
    }

    console.log(`Successfully fetched ${files.length} files`);
    return { files };

  } catch (error) {
    console.error('Error fetching drive files:', error.message);

    // Return mock data as fallback
    console.log('Falling back to mock data...');
    return {
      files: [
        {
          file_id: '1',
          file_name: 'Sample Document 1.pdf',
          folder_name: 'My Documents',
          file_link: 'https://example.com/doc1.pdf',
          upload_at: new Date().toISOString()
        },
        {
          file_id: '2',
          file_name: 'Project Report.docx',
          folder_name: 'Work Files',
          file_link: 'https://example.com/report.docx',
          upload_at: new Date(Date.now() - 86400000).toISOString()
        },
        {
          file_id: '3',
          file_name: 'Presentation.pptx',
          folder_name: 'Presentations',
          file_link: 'https://example.com/presentation.pptx',
          upload_at: new Date(Date.now() - 172800000).toISOString()
        }
      ],
      _isMockData: true,
      _error: error.message
    };
  }
}