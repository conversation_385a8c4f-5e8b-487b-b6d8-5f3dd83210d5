export async function fetchDriveFiles() {
  const response = await fetch('https://hedgehog-enjoyed-evidently.ngrok-free.app/drive/list/databaseMobile/');
  const contentType = response.headers.get('content-type');
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  if (contentType && contentType.indexOf('application/json') !== -1) {
    return await response.json();
  } else {
    const text = await response.text();
    throw new Error('Server did not return JSON: ' + text.slice(0, 100));
  }
}