export async function fetchDriveFiles() {
  // Temporary mock data while the server is not available
  // Replace this with the actual API call when your server is running

  try {
    const response = await fetch('https://hedgehog-enjoyed-evidently.ngrok-free.app/drive/list/databaseMobile/');
    const contentType = response.headers.get('content-type');
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    if (contentType && contentType.indexOf('application/json') !== -1) {
      return await response.json();
    } else {
      const text = await response.text();
      throw new Error('Server did not return JSON: ' + text.slice(0, 100));
    }
  } catch (error) {
    console.log('Server not available, using mock data:', error.message);

    // Return mock data for demonstration
    return {
      files: [
        {
          file_id: '1',
          file_name: 'Sample Document 1.pdf',
          folder_name: 'My Documents',
          file_link: 'https://example.com/doc1.pdf',
          upload_at: new Date().toISOString()
        },
        {
          file_id: '2',
          file_name: 'Project Report.docx',
          folder_name: 'Work Files',
          file_link: 'https://example.com/report.docx',
          upload_at: new Date(Date.now() - 86400000).toISOString() // Yesterday
        },
        {
          file_id: '3',
          file_name: 'Presentation.pptx',
          folder_name: 'Presentations',
          file_link: 'https://example.com/presentation.pptx',
          upload_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
        }
      ]
    };
  }
}