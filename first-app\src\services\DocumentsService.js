/**
 * CONFIGURATION FOR YOUR DJANGO BACKEND
 *
 * Using only the exact URLs from your Django backend:
 *
 * 1. /auth/ - Authentication endpoint
 * 2. /list/ - List drive files
 * 3. /documents/ - Documents endpoint
 */

const API_CONFIG = {
  // Update this URL to your current ngrok or server URL
  BASE_URL: 'https://hedgehog-enjoyed-evidently.ngrok-free.app',
  ENDPOINTS: {
    // Using your exact Django URLs
    AUTH: '/auth/',
    DRIVE_FILES: '/list/',
    DOCUMENTS: '/documents/',
  },
  // Add authentication headers if your backend requires them
  HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    // Add ngrok bypass header to avoid browser warning page
    'ngrok-skip-browser-warning': 'true',
    // Disable CSRF for API requests (common for mobile apps)
    'X-Requested-With': 'XMLHttpRequest',
    // Add authorization header if needed:
    // 'Authorization': 'Bearer YOUR_TOKEN_HERE',
  }
};

// Helper function to make API requests
async function makeApiRequest(endpoint, options = {}) {
  const url = `${API_CONFIG.BASE_URL}${endpoint}`;

  const defaultOptions = {
    method: 'GET',
    headers: {
      ...API_CONFIG.HEADERS,
      ...options.headers,
    },
    ...options,
  };

  console.log(`Making API request to: ${url}`);

  try {
    const response = await fetch(url, defaultOptions);

    console.log(`Response status: ${response.status}`);
    console.log(`Response headers:`, Object.fromEntries(response.headers.entries()));

    const contentType = response.headers.get('content-type') || '';

    // Check if response is HTML (authentication redirect)
    if (contentType.includes('text/html')) {
      const htmlText = await response.text();
      if (htmlText.includes('Google Drive Authentication') || htmlText.includes('<title>')) {
        throw new Error('Authentication required: Server returned login page. Please authenticate your backend with Google Drive.');
      }
      throw new Error(`Server returned HTML instead of JSON. This usually means authentication is required.`);
    }

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API Error ${response.status}:`, errorText);
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    if (contentType.includes('application/json')) {
      const data = await response.json();
      console.log('API Response data:', data);
      return data;
    } else {
      const text = await response.text();
      console.warn('Non-JSON response received:', text.slice(0, 100));
      throw new Error(`Server returned unexpected content type: ${contentType}`);
    }
  } catch (error) {
    if (error.name === 'TypeError' && error.message.includes('fetch')) {
      throw new Error(`Network error: Unable to connect to server at ${url}. Please check if the server is running.`);
    }
    throw error;
  }
}

// Main function to fetch drive files with fallback endpoints
export async function fetchDriveFiles() {
  // List of your exact Django endpoints to try in order
  const endpointsToTry = [
    '/documents/',  // Try documents first (after auth)
    '/list/',       // Fallback to list
  ];

  let lastError = null;

  // Try each endpoint until one works
  for (const endpoint of endpointsToTry) {
    try {
      console.log(`Trying endpoint: ${endpoint}`);
      const data = await makeApiRequest(endpoint);

      // Validate the response structure
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid response format: Expected an object');
      }

      // Handle different possible response formats from your Django backend
      let files = [];

      if (Array.isArray(data)) {
        // If the response is directly an array of files
        files = data;
      } else if (data.files && Array.isArray(data.files)) {
        // If the response has a 'files' property
        files = data.files;
      } else if (data.data && Array.isArray(data.data)) {
        // If the response has a 'data' property
        files = data.data;
      } else if (data.results && Array.isArray(data.results)) {
        // If the response has a 'results' property (Django REST framework)
        files = data.results;
      } else if (data.documents && Array.isArray(data.documents)) {
        // If the response has a 'documents' property
        files = data.documents;
      } else {
        console.warn('Unexpected response format:', data);
        throw new Error('Response does not contain a recognizable files array');
      }

      console.log(`Successfully fetched ${files.length} files from ${endpoint}`);
      return { files, endpoint }; // Return which endpoint worked

    } catch (error) {
      console.error(`Error with endpoint ${endpoint}:`, error.message);
      lastError = error;

      // If it's a 401/403, try the next endpoint
      if (error.message.includes('HTTP 401') || error.message.includes('HTTP 403')) {
        continue;
      }

      // For other errors, stop trying
      break;
    }
  }

  // If all endpoints failed, throw the last error
  console.error('All endpoints failed. Last error:', lastError?.message);
  throw lastError || new Error('All API endpoints failed');
}

// Authentication function using your /auth/ endpoint
export async function authenticateUser() {
  try {
    const data = await makeApiRequest(API_CONFIG.ENDPOINTS.AUTH);
    console.log('Authentication response:', data);
    return data;
  } catch (error) {
    console.error('Error authenticating:', error.message);
    throw error;
  }
}

// Additional API functions based on your Django backend

// Get document details using your /documents/ endpoint
export async function getDocumentDetails(fileId) {
  try {
    const data = await makeApiRequest(`${API_CONFIG.ENDPOINTS.DOCUMENTS}${fileId}/`);
    return data;
  } catch (error) {
    console.error('Error fetching document details:', error.message);
    throw error;
  }
}