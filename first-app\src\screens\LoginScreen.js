import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';

export default function LoginScreen({ navigation }) {
  const handleGoogleLogin = () => {
    navigation.replace('Home');
  };

  return (
    <View style={styles.background}>
      <View style={styles.card}>
        <View style={styles.logoContainer}>
          {/* Remplace l'emoji par une image si tu veux */}
          <Text style={styles.logo}>🌟</Text>
        </View>
        <Text style={styles.title}>Sign in with Google</Text>
        <TouchableOpacity style={styles.button} onPress={handleGoogleLogin} activeOpacity={0.85}>
          {/* Remplace l'emoji par une image Google si tu veux */}
          <Text style={styles.googleIcon}>🔵</Text>
          <Text style={styles.buttonText}>Continue with Google</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  background: {
    flex: 1,
    backgroundColor: '#e9ecef',
    justifyContent: 'center',
    alignItems: 'center',
  },
  card: {
    width: '90%',
    maxWidth: 350,
    backgroundColor: '#fff',
    borderRadius: 24,
    paddingVertical: 48,
    paddingHorizontal: 28,
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOpacity: 0.10,
    shadowRadius: 16,
    shadowOffset: { width: 0, height: 8 },
  },
  logoContainer: {
    marginBottom: 24,
    backgroundColor: '#f6f8fa',
    borderRadius: 50,
    padding: 18,
    elevation: 2,
    shadowColor: '#000',
    shadowOpacity: 0.08,
    shadowRadius: 8,
    shadowOffset: { width: 0, height: 2 },
  },
  logo: {
    fontSize: 48,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    marginBottom: 32,
    color: '#222',
    textAlign: 'center',
    letterSpacing: 0.5,
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4285F4',
    paddingVertical: 14,
    paddingHorizontal: 32,
    borderRadius: 8,
    elevation: 2,
  },
  googleIcon: {
    fontSize: 22,
    marginRight: 12,
    color: '#fff',
  },
  buttonText: {
    color: '#fff',
    fontSize: 17,
    fontWeight: '600',
  },
});